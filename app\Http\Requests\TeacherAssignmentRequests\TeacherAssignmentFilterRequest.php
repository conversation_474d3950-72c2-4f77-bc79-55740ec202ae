<?php

namespace App\Http\Requests\TeacherAssignmentRequests;

use Illuminate\Foundation\Http\FormRequest;

class TeacherAssignmentFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'teacher_id' => ['sometimes', 'nullable', 'integer', 'exists:teachers,id'],
            'subject_id' => ['sometimes', 'nullable', 'integer', 'exists:subjects,id'],
            'classroom_id' => ['sometimes', 'nullable', 'integer', 'exists:classrooms,id'],
            'academic_year_id' => ['sometimes', 'nullable', 'integer', 'exists:academic_years,id'],
            'program_id' => ['sometimes', 'nullable', 'integer', 'exists:programs,id'],
            'is_homeroom_teacher' => ['sometimes', 'nullable', 'boolean'],
            'search' => ['sometimes', 'nullable', 'string', 'max:255'],
            'sort_column' => ['sometimes', 'nullable', 'string', 'in:teacher_name,subject_name,classroom_name,academic_year_name,created_at,is_homeroom_teacher'],
            'sort_direction' => ['sometimes', 'nullable', 'string', 'in:asc,desc'],
            'per_page' => ['sometimes', 'nullable', 'integer', 'min:10', 'max:100'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'teacher_id' => 'Guru',
            'subject_id' => 'Mata Pelajaran',
            'classroom_id' => 'Kelas',
            'academic_year_id' => 'Tahun Akademik',
            'program_id' => 'Program',
            'is_homeroom_teacher' => 'Jenis Penugasan',
            'search' => 'Pencarian',
            'sort_column' => 'Kolom Pengurutan',
            'sort_direction' => 'Arah Pengurutan',
            'per_page' => 'Jumlah Per Halaman',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'teacher_id.exists' => 'Guru yang dipilih tidak valid.',
            'subject_id.exists' => 'Mata pelajaran yang dipilih tidak valid.',
            'classroom_id.exists' => 'Kelas yang dipilih tidak valid.',
            'academic_year_id.exists' => 'Tahun akademik yang dipilih tidak valid.',
            'program_id.exists' => 'Program yang dipilih tidak valid.',
            'is_homeroom_teacher.boolean' => 'Jenis penugasan harus berupa nilai boolean.',
            'sort_column.in' => 'Kolom pengurutan tidak valid.',
            'sort_direction.in' => 'Arah pengurutan harus asc atau desc.',
            'per_page.min' => 'Jumlah per halaman minimal 10.',
            'per_page.max' => 'Jumlah per halaman maksimal 100.',
        ];
    }
}
