<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TeacherAssignmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'teacher' => [
                'id' => $this->teacher?->id,
                'name' => $this->teacher?->user?->name,
                'email' => $this->teacher?->user?->email,
                'gender' => $this->teacher?->gender?->value,
                'status' => $this->teacher?->user?->status?->value,
            ],
            'subject' => [
                'id' => $this->subject?->id,
                'name' => $this->subject?->name,
                'program' => [
                    'id' => $this->subject?->program?->id,
                    'name' => $this->subject?->program?->name,
                    'code' => $this->subject?->program?->code,
                ],
            ],
            'classroom' => [
                'id' => $this->classroom?->id,
                'name' => $this->classroom?->name,
                'level' => $this->classroom?->level,
                'status' => $this->classroom?->status?->value,
                'program' => [
                    'id' => $this->classroom?->program?->id,
                    'name' => $this->classroom?->program?->name,
                ],
            ],
            'academic_year' => [
                'id' => $this->academicYear?->id,
                'name' => $this->academicYear?->name,
                'semester' => $this->academicYear?->semester?->value,
                'status' => $this->academicYear?->status?->value,
            ],
            'is_homeroom_teacher' => $this->is_homeroom_teacher,
            'assignment_type' => $this->is_homeroom_teacher ? 'Wali Kelas' : 'Guru Mapel',
            'has_schedules' => $this->classSchedules()->exists(),
            'active_schedules_count' => $this->classSchedules()->where('status', 'active')->count(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
