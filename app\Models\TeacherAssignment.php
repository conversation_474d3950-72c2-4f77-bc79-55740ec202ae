<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TeacherAssignment extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'teacher_id',
        'subject_id',
        'classroom_id',
        'academic_year_id',
        'is_homeroom_teacher',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_homeroom_teacher' => 'boolean',
    ];

    /**
     * Get the teacher that owns the assignment.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    /**
     * Get the subject that owns the assignment.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the classroom that owns the assignment.
     */
    public function classroom(): BelongsTo
    {
        return $this->belongsTo(Classroom::class);
    }

    /**
     * Get the academic year that owns the assignment.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the class schedules for the teacher assignment
     */
    public function classSchedules(): Has<PERSON>any
    {
        return $this->hasMany(ClassSchedule::class);
    }

    // ==================== SCOPES ====================

    /**
     * Scope to filter by active academic year
     */
    public function scopeActiveAcademicYear($query)
    {
        return $query->whereHas('academicYear', function ($q) {
            $q->where('status', 'active');
        });
    }

    /**
     * Scope to filter by homeroom teachers only
     */
    public function scopeHomeroomTeachers($query)
    {
        return $query->where('is_homeroom_teacher', true);
    }

    /**
     * Scope to filter by subject teachers only
     */
    public function scopeSubjectTeachers($query)
    {
        return $query->where('is_homeroom_teacher', false);
    }

    /**
     * Scope to filter by specific teacher
     */
    public function scopeByTeacher($query, $teacherId)
    {
        return $query->where('teacher_id', $teacherId);
    }

    /**
     * Scope to filter by specific classroom
     */
    public function scopeByClassroom($query, $classroomId)
    {
        return $query->where('classroom_id', $classroomId);
    }

    /**
     * Scope to filter by specific subject
     */
    public function scopeBySubject($query, $subjectId)
    {
        return $query->where('subject_id', $subjectId);
    }

    /**
     * Scope to filter by specific academic year
     */
    public function scopeByAcademicYear($query, $academicYearId)
    {
        return $query->where('academic_year_id', $academicYearId);
    }

    /**
     * Scope to filter by program through subject relationship
     */
    public function scopeByProgram($query, $programId)
    {
        return $query->whereHas('subject', function ($q) use ($programId) {
            $q->where('program_id', $programId);
        });
    }

    /**
     * Scope to include only active teachers
     */
    public function scopeActiveTeachers($query)
    {
        return $query->whereHas('teacher.user', function ($q) {
            $q->where('status', 'active');
        });
    }

    /**
     * Scope to include only active classrooms
     */
    public function scopeActiveClassrooms($query)
    {
        return $query->whereHas('classroom', function ($q) {
            $q->where('status', 'active');
        });
    }

    /**
     * Scope for optimized eager loading with all relationships
     */
    public function scopeWithFullRelations($query)
    {
        return $query->with([
            'teacher:id,user_id,birth_place,gender',
            'teacher.user:id,name,email,status',
            'subject:id,name,program_id',
            'subject.program:id,name,code',
            'classroom:id,name,level,status,program_id',
            'academicYear:id,name,semester,status'
        ]);
    }

    /**
     * Scope for basic eager loading (lighter version)
     */
    public function scopeWithBasicRelations($query)
    {
        return $query->with([
            'teacher.user:id,name',
            'subject:id,name',
            'classroom:id,name',
            'academicYear:id,name'
        ]);
    }

    // ==================== QUERY METHODS ====================

    /**
     * Get assignments for a specific teacher in current academic year
     */
    public static function getTeacherCurrentAssignments($teacherId)
    {
        return static::byTeacher($teacherId)
            ->activeAcademicYear()
            ->withBasicRelations()
            ->get();
    }

    /**
     * Get homeroom teacher for a specific classroom in current academic year
     */
    public static function getClassroomHomeroomTeacher($classroomId, $academicYearId = null)
    {
        $query = static::byClassroom($classroomId)
            ->homeroomTeachers()
            ->withBasicRelations();

        if ($academicYearId) {
            $query->byAcademicYear($academicYearId);
        } else {
            $query->activeAcademicYear();
        }

        return $query->first();
    }

    /**
     * Get all subject teachers for a specific classroom
     */
    public static function getClassroomSubjectTeachers($classroomId, $academicYearId = null)
    {
        $query = static::byClassroom($classroomId)
            ->subjectTeachers()
            ->withBasicRelations();

        if ($academicYearId) {
            $query->byAcademicYear($academicYearId);
        } else {
            $query->activeAcademicYear();
        }

        return $query->get();
    }

    /**
     * Check if teacher is already assigned to classroom for specific subject
     */
    public static function isTeacherAssignedToClassroomSubject($teacherId, $classroomId, $subjectId, $academicYearId, $excludeId = null)
    {
        $query = static::byTeacher($teacherId)
            ->byClassroom($classroomId)
            ->bySubject($subjectId)
            ->byAcademicYear($academicYearId);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Check if classroom already has a homeroom teacher
     */
    public static function classroomHasHomeroomTeacher($classroomId, $academicYearId, $excludeId = null)
    {
        $query = static::byClassroom($classroomId)
            ->byAcademicYear($academicYearId)
            ->homeroomTeachers();

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }
}
