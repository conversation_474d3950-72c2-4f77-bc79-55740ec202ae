<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('teacher_assignments', function (Blueprint $table) {
            // Composite indexes for common query patterns
            $table->index(['teacher_id', 'academic_year_id'], 'idx_teacher_academic_year');
            $table->index(['classroom_id', 'academic_year_id'], 'idx_classroom_academic_year');
            $table->index(['subject_id', 'academic_year_id'], 'idx_subject_academic_year');
            $table->index(['classroom_id', 'is_homeroom_teacher'], 'idx_classroom_homeroom');
            $table->index(['teacher_id', 'classroom_id', 'subject_id'], 'idx_teacher_classroom_subject');
            
            // Index for filtering by homeroom teacher status
            $table->index('is_homeroom_teacher', 'idx_is_homeroom_teacher');
            
            // Index for common sorting
            $table->index('created_at', 'idx_created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('teacher_assignments', function (Blueprint $table) {
            $table->dropIndex('idx_teacher_academic_year');
            $table->dropIndex('idx_classroom_academic_year');
            $table->dropIndex('idx_subject_academic_year');
            $table->dropIndex('idx_classroom_homeroom');
            $table->dropIndex('idx_teacher_classroom_subject');
            $table->dropIndex('idx_is_homeroom_teacher');
            $table->dropIndex('idx_created_at');
        });
    }
};
