<?php

namespace App\Http\Requests\TeacherAssignmentRequests;

use Illuminate\Foundation\Http\FormRequest;

class TeacherAssignmentDeleteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled by the controller's authorizeResource
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'ids' => 'required|array|min:1',
            'ids.*' => 'required|integer|exists:teacher_assignments,id',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'ids.required' => 'ID penugasan guru wajib dipilih.',
            'ids.array' => 'ID penugasan guru harus berupa array.',
            'ids.min' => 'Minimal pilih satu penugasan guru.',
            'ids.*.required' => 'ID penugasan guru tidak boleh kosong.',
            'ids.*.integer' => 'ID penugasan guru harus berupa angka.',
            'ids.*.exists' => 'Penugasan guru yang dipilih tidak valid.',
        ];
    }
}
