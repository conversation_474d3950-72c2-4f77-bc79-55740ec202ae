<?php

namespace App\Http\Requests\TeacherAssignmentRequests;

use App\Models\TeacherAssignment;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class TeacherAssignmentStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'teacher_id' => [
                'required',
                'integer',
                'exists:teachers,id',
                function ($attribute, $value, $fail) {
                    // Check if teacher user is active
                    $teacher = \App\Models\Teacher::with('user')->find($value);
                    if (!$teacher || $teacher->user->status !== 'active') {
                        $fail('Guru yang dipilih tidak aktif.');
                    }
                }
            ],
            'subject_id' => [
                'nullable',
                'integer',
                'exists:subjects,id',
                'required_if:is_homeroom_teacher,false'
            ],
            'classroom_id' => [
                'required',
                'integer',
                'exists:classrooms,id',
                function ($attribute, $value, $fail) {
                    // Check if classroom is active
                    $classroom = \App\Models\Classroom::find($value);
                    if (!$classroom || $classroom->status !== 'active') {
                        $fail('Kelas yang dipilih tidak aktif.');
                    }
                }
            ],
            'academic_year_id' => [
                'required',
                'integer',
                'exists:academic_years,id',
                function ($attribute, $value, $fail) {
                    // Check if academic year is active or planned
                    $academicYear = \App\Models\AcademicYear::find($value);
                    if (!$academicYear || !in_array($academicYear->status->value, ['active', 'planned'])) {
                        $fail('Tahun akademik yang dipilih tidak valid untuk penugasan.');
                    }
                }
            ],
            'is_homeroom_teacher' => 'nullable|boolean',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            $this->validateBusinessRules($validator);
        });
    }

    /**
     * Validate business rules
     */
    protected function validateBusinessRules(Validator $validator): void
    {
        $data = $this->validated();

        // Rule 1: Check for duplicate assignment (same teacher, subject, classroom, academic year)
        if (isset($data['subject_id'])) {
            $exists = TeacherAssignment::isTeacherAssignedToClassroomSubject(
                $data['teacher_id'],
                $data['classroom_id'],
                $data['subject_id'],
                $data['academic_year_id']
            );

            if ($exists) {
                $validator->errors()->add('subject_id', 'Guru sudah ditugaskan untuk mata pelajaran ini di kelas yang sama pada tahun akademik ini.');
            }
        }

        // Rule 2: Check if classroom already has homeroom teacher
        if ($data['is_homeroom_teacher'] ?? false) {
            $hasHomeroom = TeacherAssignment::classroomHasHomeroomTeacher(
                $data['classroom_id'],
                $data['academic_year_id']
            );

            if ($hasHomeroom) {
                $validator->errors()->add('is_homeroom_teacher', 'Kelas ini sudah memiliki wali kelas untuk tahun akademik ini.');
            }
        }

        // Rule 3: Validate subject belongs to classroom's program
        if (isset($data['subject_id']) && isset($data['classroom_id'])) {
            $classroom = \App\Models\Classroom::with('program')->find($data['classroom_id']);
            $subject = \App\Models\Subject::find($data['subject_id']);

            if ($classroom && $subject && $classroom->program_id !== $subject->program_id) {
                $validator->errors()->add('subject_id', 'Mata pelajaran tidak sesuai dengan program kelas yang dipilih.');
            }
        }
    }

    /**
     * Get custom validation messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'teacher_id.required' => 'Guru wajib dipilih',
            'teacher_id.exists' => 'Guru yang dipilih tidak valid',
            'subject_id.exists' => 'Mata pelajaran yang dipilih tidak valid',
            'subject_id.required_if' => 'Mata pelajaran wajib dipilih untuk guru mapel',
            'classroom_id.required' => 'Kelas wajib dipilih',
            'classroom_id.exists' => 'Kelas yang dipilih tidak valid',
            'academic_year_id.required' => 'Tahun akademik wajib dipilih',
            'academic_year_id.exists' => 'Tahun akademik yang dipilih tidak valid',
            'is_homeroom_teacher.boolean' => 'Status wali kelas harus berupa nilai boolean',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'teacher_id' => 'Guru',
            'subject_id' => 'Mata Pelajaran',
            'classroom_id' => 'Kelas',
            'academic_year_id' => 'Tahun Akademik',
            'is_homeroom_teacher' => 'Status Wali Kelas',
        ];
    }
}
