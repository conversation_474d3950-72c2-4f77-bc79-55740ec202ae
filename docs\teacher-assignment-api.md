# Teacher Assignment API Documentation

## Overview
API endpoints untuk mengelola penugasan guru (Teacher Assignment) dengan optimasi query dan validasi business logic.

## Endpoints

### 1. List Teacher Assignments
**GET** `/admin/teacher-assignments`

**Parameters:**
- `teacher_id` (optional): Filter by teacher ID
- `subject_id` (optional): Filter by subject ID  
- `classroom_id` (optional): Filter by classroom ID
- `academic_year_id` (optional): Filter by academic year ID
- `program_id` (optional): Filter by program ID
- `is_homeroom_teacher` (optional): Filter by homeroom teacher status (true/false)
- `search` (optional): Search in teacher name, email, subject name, classroom name, academic year name

**Response:**
- HTML view for regular requests
- JSON DataTable data for AJAX requests

### 2. Create Teacher Assignment
**POST** `/admin/teacher-assignments`

**Body:**
```json
{
    "teacher_id": 1,
    "subject_id": 2,
    "classroom_id": 3,
    "academic_year_id": 4,
    "is_homeroom_teacher": false
}
```

**Validation Rules:**
- Prevents duplicate assignments (same teacher, subject, classroom, academic year)
- Prevents multiple homeroom teachers per classroom
- Validates subject belongs to classroom's program
- Ensures teacher and classroom are active

### 3. Update Teacher Assignment
**PUT** `/admin/teacher-assignments/{id}`

**Body:** Same as create

**Additional Validation:**
- Prevents changes to critical fields if active class schedules exist

### 4. Delete Teacher Assignment
**DELETE** `/admin/teacher-assignments/{id}`

**Validation:**
- Prevents deletion if active class schedules exist

### 5. Bulk Delete Teacher Assignments
**DELETE** `/admin/teacher-assignments/bulk-destroy`

**Body:**
```json
{
    "ids": [1, 2, 3]
}
```

### 6. Get Statistics
**GET** `/admin/teacher-assignments/statistics`

**Response:**
```json
{
    "total_assignments": 150,
    "homeroom_teachers": 25,
    "subject_teachers": 125,
    "active_assignments": 140,
    "assignments_by_program": [...],
    "assignments_by_academic_year": [...]
}
```

### 7. Get Subjects by Program
**GET** `/admin/teacher-assignments/subjects-by-program/{program_id}`

**Response:**
```json
[
    {
        "id": 1,
        "name": "Matematika"
    }
]
```

### 8. Get Classrooms by Program
**GET** `/admin/teacher-assignments/classrooms-by-program/{program_id}`

**Response:**
```json
[
    {
        "id": 1,
        "name": "X-A",
        "level": "10",
        "academic_year_id": 1
    }
]
```

### 9. Check Assignment Availability
**GET** `/admin/teacher-assignments/check-availability`

**Parameters:**
- `teacher_id`: Teacher ID
- `classroom_id`: Classroom ID
- `subject_id`: Subject ID
- `academic_year_id`: Academic Year ID
- `is_homeroom_teacher`: Boolean
- `exclude_id` (optional): Assignment ID to exclude from check

**Response:**
```json
{
    "available": true,
    "conflicts": []
}
```

## Model Scopes

### TeacherAssignment Scopes
- `activeAcademicYear()`: Filter by active academic year
- `homeroomTeachers()`: Filter homeroom teachers only
- `subjectTeachers()`: Filter subject teachers only
- `byTeacher($teacherId)`: Filter by specific teacher
- `byClassroom($classroomId)`: Filter by specific classroom
- `bySubject($subjectId)`: Filter by specific subject
- `byAcademicYear($academicYearId)`: Filter by specific academic year
- `byProgram($programId)`: Filter by program through subject
- `activeTeachers()`: Include only active teachers
- `activeClassrooms()`: Include only active classrooms
- `withFullRelations()`: Eager load all relationships
- `withBasicRelations()`: Eager load basic relationships

### Teacher Scopes
- `activeTeachers()`: Filter active teachers only
- `byRole($role)`: Filter by user role
- `homeroomTeachers()`: Filter teachers who are homeroom teachers
- `byProgram($programId)`: Filter by program through assignments

## Query Methods

### Static Methods in TeacherAssignment
- `getTeacherCurrentAssignments($teacherId)`: Get current assignments for teacher
- `getClassroomHomeroomTeacher($classroomId, $academicYearId)`: Get homeroom teacher for classroom
- `getClassroomSubjectTeachers($classroomId, $academicYearId)`: Get subject teachers for classroom
- `isTeacherAssignedToClassroomSubject(...)`: Check if assignment exists
- `classroomHasHomeroomTeacher(...)`: Check if classroom has homeroom teacher

## Database Optimizations

### Indexes Added
- `idx_teacher_academic_year`: (teacher_id, academic_year_id)
- `idx_classroom_academic_year`: (classroom_id, academic_year_id)
- `idx_subject_academic_year`: (subject_id, academic_year_id)
- `idx_classroom_homeroom`: (classroom_id, is_homeroom_teacher)
- `idx_teacher_classroom_subject`: (teacher_id, classroom_id, subject_id)
- `idx_is_homeroom_teacher`: (is_homeroom_teacher)
- `idx_created_at`: (created_at)

### Query Optimizations
- Specific select fields to reduce data transfer
- Optimized eager loading with relationship constraints
- Efficient filtering using direct column comparisons
- Proper use of indexes for common query patterns
