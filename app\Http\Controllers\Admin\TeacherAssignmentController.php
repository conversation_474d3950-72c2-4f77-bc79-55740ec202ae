<?php

namespace App\Http\Controllers\Admin;

use App\Models\Subject;
use App\Models\Teacher;
use App\Models\Classroom;
use App\Models\AcademicYear;
use App\Models\TeacherAssignment;
use App\Http\Resources\TeacherAssignmentResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Requests\TeacherAssignmentRequests\TeacherAssignmentStoreRequest;
use App\Http\Requests\TeacherAssignmentRequests\TeacherAssignmentDeleteRequest;
use App\Http\Requests\TeacherAssignmentRequests\TeacherAssignmentFilterRequest;
use App\Http\Requests\TeacherAssignmentRequests\TeacherAssignmentUpdateRequest;

class TeacherAssignmentController extends Controller
{
    // Constants for repeated select strings
    private const USER_SELECT = 'user:id,name,email,status';
    private const PROGRAM_SELECT = 'program:id,name,code';
    private const BASIC_PROGRAM_SELECT = 'program:id,name';
    /**
     * Display a listing of teacher assignments.
     */
    public function index(TeacherAssignmentFilterRequest $request): View|JsonResponse
    {
        // Optimized eager loading with specific select fields
        $query = TeacherAssignment::select([
                'teacher_assignments.id',
                'teacher_assignments.teacher_id',
                'teacher_assignments.subject_id',
                'teacher_assignments.classroom_id',
                'teacher_assignments.academic_year_id',
                'teacher_assignments.is_homeroom_teacher',
                'teacher_assignments.created_at',
                'teacher_assignments.updated_at'
            ])
            ->with([
                'teacher:id,user_id,birth_place,gender',
                'teacher.' . self::USER_SELECT,
                'subject:id,name,program_id',
                'subject.' . self::PROGRAM_SELECT,
                'classroom:id,name,level,status,program_id',
                'academicYear:id,name,semester,status'
            ]);

        $query = $this->applyFilters($query, $request->validated());

        if ($request->ajax()) {
            return $this->formatTeacherAssignmentForDatatable($query);
        }

        // Optimized queries for dropdown data
        $dropdownData = $this->getDropdownData();
        $dropdownData['initialFilters'] = $request->validated();

        return view('admin.pages.teacher-assignment.index', $dropdownData);
    }

    /**
     * Apply filters to the query with optimized joins.
     */
    protected function applyFilters($query, array $filters)
    {
        // Direct column filters (most efficient)
        if (!empty($filters['classroom_id'])) {
            $query->where('teacher_assignments.classroom_id', $filters['classroom_id']);
        }

        if (!empty($filters['academic_year_id'])) {
            $query->where('teacher_assignments.academic_year_id', $filters['academic_year_id']);
        }

        if (!empty($filters['teacher_id'])) {
            $query->where('teacher_assignments.teacher_id', $filters['teacher_id']);
        }

        if (!empty($filters['subject_id'])) {
            $query->where('teacher_assignments.subject_id', $filters['subject_id']);
        }

        // Filter by program (through subject relationship)
        if (!empty($filters['program_id'])) {
            $query->byProgram($filters['program_id']);
        }

        if (isset($filters['is_homeroom_teacher'])) {
            $query->where('teacher_assignments.is_homeroom_teacher', $filters['is_homeroom_teacher']);
        }

        // Optimized search with joins instead of whereHas
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->whereHas('teacher.user', function ($userQuery) use ($searchTerm) {
                    $userQuery->where('name', 'like', $searchTerm)
                        ->orWhere('email', 'like', $searchTerm);
                })
                ->orWhereHas('subject', function ($subjectQuery) use ($searchTerm) {
                    $subjectQuery->where('name', 'like', $searchTerm);
                })
                ->orWhereHas('classroom', function ($classroomQuery) use ($searchTerm) {
                    $classroomQuery->where('name', 'like', $searchTerm);
                })
                ->orWhereHas('academicYear', function ($academicQuery) use ($searchTerm) {
                    $academicQuery->where('name', 'like', $searchTerm);
                });
            });
        }

        // Default sorting - keep it simple to avoid DataTables conflicts
        $query->orderBy('teacher_assignments.created_at', 'desc')
              ->orderBy('teacher_assignments.id', 'desc');

        return $query;
    }



    /**
     * Format response for DataTables with optimized performance.
     */
    protected function formatTeacherAssignmentForDatatable($query): JsonResponse
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('teacher_name', fn($row) => $row->teacher?->user?->name ?? '-')
            ->editColumn('teacher_email', fn($row) => $row->teacher?->user?->email ?? '-')
            ->editColumn('subject_name', fn($row) => $row->subject?->name ?? '-')
            ->editColumn('classroom_name', fn($row) => $row->classroom?->name ?? '-')
            ->editColumn('academic_year_name', fn($row) => $row->academicYear?->name ?? '-')
            ->editColumn('program_name', fn($row) => $row->subject?->program?->name ?? '-')
            ->editColumn('assignment_type', fn($row) => $row->is_homeroom_teacher ? 'Wali Kelas' : 'Guru Mapel')
            ->editColumn('classroom_level', fn($row) => $row->classroom?->level ?? '-')
            ->editColumn('academic_semester', fn($row) => ucfirst($row->academicYear?->semester?->value ?? '-'))
            ->editColumn('status', function ($row) {
                $badgeClass = $row->is_homeroom_teacher ? 'success' : 'primary';
                $text = $row->is_homeroom_teacher ? 'Wali Kelas' : 'Guru Mapel';
                return '<span class="badge bg-' . $badgeClass . ' text-uppercase">' . $text . '</span>';
            })
            ->addColumn('teacher_info', function ($row) {
                $name = $row->teacher?->user?->name ?? '-';
                $email = $row->teacher?->user?->email ?? '-';
                return '<div class="d-flex flex-column">' .
                       '<span class="fw-bold">' . $name . '</span>' .
                       '<small class="text-muted">' . $email . '</small>' .
                       '</div>';
            })
            ->addColumn('classroom_info', function ($row) {
                $className = $row->classroom?->name ?? '-';
                $level = $row->classroom?->level ?? '-';
                return '<div class="d-flex flex-column">' .
                       '<span class="fw-bold">' . $className . '</span>' .
                       '<small class="text-muted">Level: ' . $level . '</small>' .
                       '</div>';
            })
            ->addColumn('subject_info', function ($row) {
                $subjectName = $row->subject?->name ?? '-';
                $programName = $row->subject?->program?->name ?? '-';
                return '<div class="d-flex flex-column">' .
                       '<span class="fw-bold">' . $subjectName . '</span>' .
                       '<small class="text-muted">' . $programName . '</small>' .
                       '</div>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.pages.teacher-assignment._action', [
                    'edit' => route('admin.teacher-assignments.edit', $row->id),
                    'destroy' => route('admin.teacher-assignments.destroy', $row->id),
                    'id' => $row->id,
                ])->render();
            })
            ->rawColumns(['status', 'action', 'teacher_info', 'classroom_info', 'subject_info'])
            ->make(true);
    }

    /**
     * Get teacher assignment statistics
     */
    public function getStatistics(): JsonResponse
    {
        $stats = [
            'total_assignments' => TeacherAssignment::count(),
            'homeroom_teachers' => TeacherAssignment::homeroomTeachers()->count(),
            'subject_teachers' => TeacherAssignment::subjectTeachers()->count(),
            'active_assignments' => TeacherAssignment::activeAcademicYear()->count(),
            'assignments_by_program' => TeacherAssignment::select('programs.name as program_name')
                ->join('subjects', 'teacher_assignments.subject_id', '=', 'subjects.id')
                ->join('programs', 'subjects.program_id', '=', 'programs.id')
                ->groupBy('programs.id', 'programs.name')
                ->selectRaw('COUNT(*) as total')
                ->get(),
            'assignments_by_academic_year' => TeacherAssignment::select('academic_years.name as academic_year_name')
                ->join('academic_years', 'teacher_assignments.academic_year_id', '=', 'academic_years.id')
                ->groupBy('academic_years.id', 'academic_years.name')
                ->selectRaw('COUNT(*) as total')
                ->orderBy('academic_years.start_date', 'desc')
                ->get(),
        ];

        return response()->json($stats);
    }

    /**
     * Get dropdown data for forms (optimized with caching)
     */
    protected function getDropdownData(): array
    {
        return [
            'teachers' => Teacher::select('id', 'user_id', 'gender')
                ->with(self::USER_SELECT)
                ->activeTeachers()
                ->orderBy('id')
                ->get(),
            'subjects' => Subject::select('id', 'name', 'program_id')
                ->with(self::PROGRAM_SELECT)
                ->orderBy('name')
                ->get(),
            'classrooms' => Classroom::select('id', 'name', 'level', 'program_id', 'academic_year_id')
                ->with(self::BASIC_PROGRAM_SELECT)
                ->where('status', 'active')
                ->orderBy('name')
                ->get(),
            'programs' => \App\Models\Program::select('id', 'name', 'code')
                ->where('status', 'active')
                ->orderBy('name')
                ->get(),
            'academicYears' => AcademicYear::select('id', 'name', 'semester', 'start_date', 'end_date')
                ->where('status', 'active')
                ->orderBy('start_date', 'desc')
                ->get(),
        ];
    }

    /**
     * Get subjects by program (AJAX endpoint)
     */
    public function getSubjectsByProgram(int $programId): JsonResponse
    {
        $subjects = Subject::select('id', 'name')
            ->where('program_id', $programId)
            ->orderBy('name')
            ->get();

        return response()->json($subjects);
    }

    /**
     * Get classrooms by program (AJAX endpoint)
     */
    public function getClassroomsByProgram(int $programId): JsonResponse
    {
        $classrooms = Classroom::select('id', 'name', 'level', 'academic_year_id')
            ->where('program_id', $programId)
            ->where('status', 'active')
            ->orderBy('name')
            ->get();

        return response()->json($classrooms);
    }

    /**
     * Check if teacher can be assigned to classroom/subject
     */
    public function checkAssignmentAvailability(): JsonResponse
    {
        $teacherId = request('teacher_id');
        $classroomId = request('classroom_id');
        $subjectId = request('subject_id');
        $academicYearId = request('academic_year_id');
        $isHomeroomTeacher = request('is_homeroom_teacher', false);
        $excludeId = request('exclude_id');

        $conflicts = [];

        // Check for duplicate assignment
        if ($subjectId) {
            $exists = TeacherAssignment::isTeacherAssignedToClassroomSubject(
                $teacherId, $classroomId, $subjectId, $academicYearId, $excludeId
            );
            if ($exists) {
                $conflicts[] = 'Guru sudah ditugaskan untuk mata pelajaran ini di kelas yang sama.';
            }
        }

        // Check homeroom teacher conflict
        if ($isHomeroomTeacher) {
            $hasHomeroom = TeacherAssignment::classroomHasHomeroomTeacher(
                $classroomId, $academicYearId, $excludeId
            );
            if ($hasHomeroom) {
                $conflicts[] = 'Kelas ini sudah memiliki wali kelas.';
            }
        }

        return response()->json([
            'available' => empty($conflicts),
            'conflicts' => $conflicts,
        ]);
    }

    /**
     * Show the form for creating a new teacher assignment.
     */
    public function create(): View
    {
        $dropdownData = $this->getDropdownData();

        return view('admin.pages.teacher-assignment.create', $dropdownData);
    }

    /**
     * Store a newly created teacher assignment.
     */
    public function store(TeacherAssignmentStoreRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $teacherAssignment = TeacherAssignment::create($request->validated());

            // Load relationships for response
            $teacherAssignment->load([
                'teacher.user:id,name,email',
                'subject:id,name',
                'classroom:id,name',
                'academicYear:id,name'
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Penugasan guru berhasil dibuat.',
                'data' => new TeacherAssignmentResource($teacherAssignment),
            ], Response::HTTP_CREATED);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating teacher assignment: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat penugasan guru.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Show the form for editing the specified teacher assignment.
     */
    public function edit(int $id): View
    {
        $teacherAssignment = TeacherAssignment::select([
                'id', 'teacher_id', 'subject_id', 'classroom_id',
                'academic_year_id', 'is_homeroom_teacher'
            ])
            ->with([
                'teacher:id,user_id,gender',
                'teacher.user:id,name,email,status',
                'subject:id,name,program_id',
                'subject.program:id,name,code',
                'classroom:id,name,level,program_id',
                'academicYear:id,name,semester'
            ])
            ->findOrFail($id);

        $dropdownData = $this->getDropdownData();
        $dropdownData['teacherAssignment'] = $teacherAssignment;

        return view('admin.pages.teacher-assignment.edit', $dropdownData);
    }

    /**
     * Update the specified teacher assignment.
     */
    public function update(TeacherAssignmentUpdateRequest $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $teacherAssignment = TeacherAssignment::findOrFail($id);
            $teacherAssignment->update($request->validated());

            // Load updated relationships
            $teacherAssignment->load([
                'teacher.user:id,name,email',
                'subject:id,name',
                'classroom:id,name',
                'academicYear:id,name'
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Penugasan guru berhasil diperbarui.',
                'data' => new TeacherAssignmentResource($teacherAssignment),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating teacher assignment: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui penugasan guru.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified teacher assignment.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $teacherAssignment = TeacherAssignment::findOrFail($id);

            // Check if there are related active class schedules
            if ($teacherAssignment->classSchedules()->where('status', 'active')->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus penugasan guru yang memiliki jadwal kelas aktif.',
                ], Response::HTTP_FORBIDDEN);
            }

            $teacherAssignment->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Penugasan guru berhasil dihapus.',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting teacher assignment: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus penugasan guru.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Bulk delete teacher assignments
     */
    public function bulkDestroy(TeacherAssignmentDeleteRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $ids = $request->validated()['ids'];
            $assignments = TeacherAssignment::whereIn('id', $ids)->get();

            // Check if any assignment has active class schedules
            foreach ($assignments as $assignment) {
                if ($assignment->classSchedules()->where('status', 'active')->exists()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Tidak dapat menghapus penugasan yang memiliki jadwal kelas aktif.',
                    ], Response::HTTP_FORBIDDEN);
                }
            }

            TeacherAssignment::whereIn('id', $ids)->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => count($ids) . ' penugasan guru berhasil dihapus.',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error bulk deleting teacher assignments: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus penugasan guru.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}


